package com.lysjk.controller;

import com.lysjk.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/excel")
@Slf4j
public class ExcelController {
    public Result parseExcel(MultipartFile file){

    }
}
